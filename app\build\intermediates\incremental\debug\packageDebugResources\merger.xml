<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res"><file name="slide_in_left" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\anim\slide_in_left.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_out_left" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\anim\slide_out_left.xml" qualifiers="" type="anim"/><file name="slide_out_right" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\anim\slide_out_right.xml" qualifiers="" type="anim"/><file name="account_outline" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\account_outline.xml" qualifiers="" type="drawable"/><file name="arrow_left_thin" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\arrow_left_thin.xml" qualifiers="" type="drawable"/><file name="arrow_right_thin" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\arrow_right_thin.xml" qualifiers="" type="drawable"/><file name="check_circle" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\check_circle.xml" qualifiers="" type="drawable"/><file name="chevron_left" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\chevron_left.xml" qualifiers="" type="drawable"/><file name="chevron_right" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\chevron_right.xml" qualifiers="" type="drawable"/><file name="crown" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\crown.xml" qualifiers="" type="drawable"/><file name="export" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\export.xml" qualifiers="" type="drawable"/><file name="filter" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\filter.xml" qualifiers="" type="drawable"/><file name="filter_outline" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\filter_outline.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="label_outline" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\label_outline.xml" qualifiers="" type="drawable"/><file name="pie_chart_24" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\pie_chart_24.xml" qualifiers="" type="drawable"/><file name="resource_import" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\resource_import.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">SplitExpenses</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.SplitExpenses" parent="android:Theme.Material.Light.NoActionBar">
        
        <item name="android:windowAnimationStyle">@style/NoAnimationStyle</item>
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@null</item>
    </style><style name="NoAnimationStyle">
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@null</item>
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="lock" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\lock.xml" qualifiers="" type="drawable"/><file name="lock_open_variant" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\lock_open_variant.xml" qualifiers="" type="drawable"/><file name="lock_open" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\lock_open.xml" qualifiers="" type="drawable"/><file name="chart_arc" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\chart_arc.xml" qualifiers="" type="drawable"/><file name="tag_outline" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\tag_outline.xml" qualifiers="" type="drawable"/><file name="wifi_off" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\wifi_off.xml" qualifiers="" type="drawable"/><file name="cloud_off_outline" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\cloud_off_outline.xml" qualifiers="" type="drawable"/><file name="sync" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\sync.xml" qualifiers="" type="drawable"/><file name="account_group_outline" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\account_group_outline.xml" qualifiers="" type="drawable"/><file name="account_multiple_plus_outline" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\account_multiple_plus_outline.xml" qualifiers="" type="drawable"/><file name="bar_chart_24" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\bar_chart_24.xml" qualifiers="" type="drawable"/><file name="calendar" path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\main\res\drawable\calendar.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\AndroidStudioProjects\SplitExpenses\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="firebase_database_url" translatable="false">https://splitexpenses-2d71b-default-rtdb.europe-west1.firebasedatabase.app</string><string name="gcm_defaultSenderId" translatable="false">175517668233</string><string name="google_api_key" translatable="false">AIzaSyBk38_5UM5yOgBHGm5d8XkdCPAkhg8vzUU</string><string name="google_app_id" translatable="false">1:175517668233:android:cc60d95b13df51c728c758</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBk38_5UM5yOgBHGm5d8XkdCPAkhg8vzUU</string><string name="google_storage_bucket" translatable="false">splitexpenses-2d71b.firebasestorage.app</string><string name="project_id" translatable="false">splitexpenses-2d71b</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>