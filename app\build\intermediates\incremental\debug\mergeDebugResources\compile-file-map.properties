#Thu May 29 16:39:56 CEST 2025
com.example.splitexpenses.app-main-77\:/anim/slide_in_left.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_in_left.xml.flat
com.example.splitexpenses.app-main-77\:/anim/slide_in_right.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_in_right.xml.flat
com.example.splitexpenses.app-main-77\:/anim/slide_out_left.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_out_left.xml.flat
com.example.splitexpenses.app-main-77\:/anim/slide_out_right.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_out_right.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/account_cash_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_account_cash_outline.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/account_group_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_account_group_outline.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/account_multiple_plus_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_account_multiple_plus_outline.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/account_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_account_outline.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/arrow_left_thin.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_arrow_left_thin.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/arrow_right_thin.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_arrow_right_thin.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/bar_chart_24.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bar_chart_24.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/calendar.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_calendar.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/chart_arc.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_chart_arc.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/check_circle.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_check_circle.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/chevron_left.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_chevron_left.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/chevron_right.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_chevron_right.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/cloud_off_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_cloud_off_outline.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/content_save_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_content_save_outline.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/crown.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_crown.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/currency_eur.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_currency_eur.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/export.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_export.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/filter.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_filter.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/filter_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_filter_outline.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/ic_launcher_background.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/label_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_label_outline.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/lock.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_lock.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/lock_open.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_lock_open.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/lock_open_variant.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_lock_open_variant.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/pie_chart_24.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_pie_chart_24.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/plus.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_plus.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/resource_import.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_resource_import.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/sync.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_sync.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/tag_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tag_outline.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/text_box_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_text_box_outline.xml.flat
com.example.splitexpenses.app-main-77\:/drawable/wifi_off.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_wifi_off.xml.flat
com.example.splitexpenses.app-main-77\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.splitexpenses.app-main-77\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.splitexpenses.app-main-77\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.splitexpenses.app-main-77\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.splitexpenses.app-main-77\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.splitexpenses.app-main-77\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.splitexpenses.app-main-77\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.splitexpenses.app-main-77\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.splitexpenses.app-main-77\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.splitexpenses.app-main-77\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.splitexpenses.app-main-77\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.splitexpenses.app-main-77\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.splitexpenses.app-main-77\:/xml/backup_rules.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.splitexpenses.app-main-77\:/xml/data_extraction_rules.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
