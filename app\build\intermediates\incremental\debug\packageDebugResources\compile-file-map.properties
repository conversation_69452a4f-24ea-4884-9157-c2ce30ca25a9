#Thu May 29 16:39:56 CEST 2025
com.example.splitexpenses.app-main-6\:/anim/slide_in_left.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_in_left.xml
com.example.splitexpenses.app-main-6\:/anim/slide_in_right.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_in_right.xml
com.example.splitexpenses.app-main-6\:/anim/slide_out_left.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_out_left.xml
com.example.splitexpenses.app-main-6\:/anim/slide_out_right.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_out_right.xml
com.example.splitexpenses.app-main-6\:/drawable/account_cash_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\account_cash_outline.xml
com.example.splitexpenses.app-main-6\:/drawable/account_group_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\account_group_outline.xml
com.example.splitexpenses.app-main-6\:/drawable/account_multiple_plus_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\account_multiple_plus_outline.xml
com.example.splitexpenses.app-main-6\:/drawable/account_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\account_outline.xml
com.example.splitexpenses.app-main-6\:/drawable/arrow_left_thin.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\arrow_left_thin.xml
com.example.splitexpenses.app-main-6\:/drawable/arrow_right_thin.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\arrow_right_thin.xml
com.example.splitexpenses.app-main-6\:/drawable/bar_chart_24.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bar_chart_24.xml
com.example.splitexpenses.app-main-6\:/drawable/calendar.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\calendar.xml
com.example.splitexpenses.app-main-6\:/drawable/chart_arc.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\chart_arc.xml
com.example.splitexpenses.app-main-6\:/drawable/check_circle.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\check_circle.xml
com.example.splitexpenses.app-main-6\:/drawable/chevron_left.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\chevron_left.xml
com.example.splitexpenses.app-main-6\:/drawable/chevron_right.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\chevron_right.xml
com.example.splitexpenses.app-main-6\:/drawable/cloud_off_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\cloud_off_outline.xml
com.example.splitexpenses.app-main-6\:/drawable/content_save_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\content_save_outline.xml
com.example.splitexpenses.app-main-6\:/drawable/crown.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\crown.xml
com.example.splitexpenses.app-main-6\:/drawable/currency_eur.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\currency_eur.xml
com.example.splitexpenses.app-main-6\:/drawable/export.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\export.xml
com.example.splitexpenses.app-main-6\:/drawable/filter.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\filter.xml
com.example.splitexpenses.app-main-6\:/drawable/filter_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\filter_outline.xml
com.example.splitexpenses.app-main-6\:/drawable/ic_launcher_background.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.example.splitexpenses.app-main-6\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.example.splitexpenses.app-main-6\:/drawable/label_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\label_outline.xml
com.example.splitexpenses.app-main-6\:/drawable/lock.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\lock.xml
com.example.splitexpenses.app-main-6\:/drawable/lock_open.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\lock_open.xml
com.example.splitexpenses.app-main-6\:/drawable/lock_open_variant.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\lock_open_variant.xml
com.example.splitexpenses.app-main-6\:/drawable/pie_chart_24.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\pie_chart_24.xml
com.example.splitexpenses.app-main-6\:/drawable/plus.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\plus.xml
com.example.splitexpenses.app-main-6\:/drawable/resource_import.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\resource_import.xml
com.example.splitexpenses.app-main-6\:/drawable/sync.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\sync.xml
com.example.splitexpenses.app-main-6\:/drawable/tag_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tag_outline.xml
com.example.splitexpenses.app-main-6\:/drawable/text_box_outline.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\text_box_outline.xml
com.example.splitexpenses.app-main-6\:/drawable/wifi_off.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\wifi_off.xml
com.example.splitexpenses.app-main-6\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.example.splitexpenses.app-main-6\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.example.splitexpenses.app-main-6\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.splitexpenses.app-main-6\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.splitexpenses.app-main-6\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.splitexpenses.app-main-6\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.splitexpenses.app-main-6\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.splitexpenses.app-main-6\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.splitexpenses.app-main-6\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.splitexpenses.app-main-6\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.splitexpenses.app-main-6\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.splitexpenses.app-main-6\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.splitexpenses.app-main-6\:/xml/backup_rules.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.example.splitexpenses.app-main-6\:/xml/data_extraction_rules.xml=C\:\\Users\\Florian\\AndroidStudioProjects\\SplitExpenses\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
