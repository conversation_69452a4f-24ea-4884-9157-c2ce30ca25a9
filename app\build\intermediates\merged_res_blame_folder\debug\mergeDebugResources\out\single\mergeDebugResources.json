[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_currency_eur.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\currency_eur.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_calendar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_account_group_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\account_group_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\anim_slide_in_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\anim\\slide_in_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\anim_slide_in_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\anim\\slide_in_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_chevron_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\chevron_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\anim_slide_out_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\anim\\slide_out_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_account_cash_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\account_cash_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_tag_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\tag_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_account_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\account_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_text_box_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\text_box_outline.xml"}, {"merged": "com.example.splitexpenses.app-debug-75:/drawable_scale_balance.xml.flat", "source": "com.example.splitexpenses.app-main-77:/drawable/scale_balance.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_chart_arc.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\chart_arc.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_label_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\label_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_arrow_right_thin.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\arrow_right_thin.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_filter_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\filter_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_content_save_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\content_save_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_plus.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\plus.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "com.example.splitexpenses.app-debug-75:/drawable_trending_up.xml.flat", "source": "com.example.splitexpenses.app-main-77:/drawable/trending_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_arrow_left_thin.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\arrow_left_thin.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_lock_open_variant.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\lock_open_variant.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_bar_chart_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\bar_chart_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\anim_slide_out_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\anim\\slide_out_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "com.example.splitexpenses.app-debug-75:/drawable_trending_down.xml.flat", "source": "com.example.splitexpenses.app-main-77:/drawable/trending_down.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_check_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\check_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_resource_import.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\resource_import.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_lock_open.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\lock_open.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_pie_chart_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\pie_chart_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_cloud_off_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\cloud_off_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_lock.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\lock.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_chevron_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\chevron_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_sync.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\sync.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_wifi_off.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\wifi_off.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_crown.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\crown.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_account_multiple_plus_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\account_multiple_plus_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-debug-75:\\drawable_export.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.splitexpenses.app-main-77:\\drawable\\export.xml"}]