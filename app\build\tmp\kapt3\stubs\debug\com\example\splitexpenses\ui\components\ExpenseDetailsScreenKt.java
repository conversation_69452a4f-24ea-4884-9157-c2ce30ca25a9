package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\u001a\u001a\u0010\u0000\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0084\u0001\u0010\u0006\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\u0016\b\u0002\u0010\f\u001a\u0010\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u0001\u0018\u00010\r2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00102\b\b\u0002\u0010\u0012\u001a\u00020\u0010H\u0007\u00a8\u0006\u0013"}, d2 = {"ExpenseContentOnly", "", "expense", "Lcom/example/splitexpenses/data/Expense;", "group", "Lcom/example/splitexpenses/data/GroupData;", "ExpenseDetailsScreen", "onBackClick", "Lkotlin/Function0;", "onEditClick", "onPreviousClick", "onNextClick", "onNavigateToExpense", "Lkotlin/Function1;", "", "hasPrevious", "", "hasNext", "isOffline", "app_debug"})
public final class ExpenseDetailsScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void ExpenseDetailsScreen(@org.jetbrains.annotations.Nullable()
    com.example.splitexpenses.data.Expense expense, @org.jetbrains.annotations.NotNull()
    com.example.splitexpenses.data.GroupData group, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onEditClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPreviousClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNextClick, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNavigateToExpense, boolean hasPrevious, boolean hasNext, boolean isOffline) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ExpenseContentOnly(com.example.splitexpenses.data.Expense expense, com.example.splitexpenses.data.GroupData group) {
    }
}